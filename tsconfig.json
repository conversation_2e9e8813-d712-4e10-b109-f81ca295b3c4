{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "jsx": "react-native", "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/contexts/*": ["./contexts/*"], "@/hooks/*": ["./hooks/*"], "@/utils/*": ["./utils/*"], "@/types/*": ["./types/*"], "@/data/*": ["./data/*"], "@/services/*": ["./services/*"], "@/lib/*": ["./lib/*"], "@/constants/*": ["./constants/*"], "@/assets/*": ["./assets/*"], "@/screens/*": ["./screens/*"], "@backend/*": ["./backend/*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules", "backend"]}