import { Tabs } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React from 'react';
import { StyleSheet, View } from 'react-native';
import FloatingWorkoutTracker from '../../components/ui/FloatingWorkoutTracker';

export default function TabLayout() {
  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      <Tabs
        screenOptions={{
          headerShown: false,
          tabBarStyle: { 
            display: 'none',
            height: 0,
            position: 'absolute',
            bottom: -100, // Hide it completely off screen
          },
          tabBarShowLabel: false,
          tabBarButton: () => null, // Remove tab bar buttons
          animation: 'shift',
        }}
        tabBar={() => null} // Completely remove the default tab bar
      >
        <Tabs.Screen
          name="index"
          options={{
            title: 'Home',
            href: '/',
          }}
        />
        <Tabs.Screen
          name="training"
          options={{
            title: 'Training',
            href: '/training',
          }}
        />
        <Tabs.Screen
          name="social"
          options={{
            title: 'Social',
            href: '/social',
          }}
        />
        <Tabs.Screen
          name="explore"
          options={{
            title: 'Explore',
            href: '/explore',
          }}
        />
        <Tabs.Screen
          name="profile"
          options={{
            title: 'Profile',
            href: '/profile',
          }}
        />
        <Tabs.Screen
          name="marketplace"
          options={{
            title: 'Marketplace',
            href: '/marketplace',
          }}
        />
        <Tabs.Screen
          name="feed"
          options={{
            title: 'Feed',
            href: '/feed',
          }}
        />
        <Tabs.Screen
          name="clubs"
          options={{
            title: 'Clubs',
            href: '/clubs',
          }}
        />
        <Tabs.Screen
          name="progress"
          options={{
            title: 'Progress',
            href: '/progress',
          }}
        />
        <Tabs.Screen
          name="settings"
          options={{
            title: 'Settings',
            href: '/settings',
          }}
        />
        {/* Hide screens not in main navigation */}
        <Tabs.Screen
          name="feed-new"
          options={{
            title: 'New Feed',
            href: null, // Don't show in navigation
          }}
        />
        <Tabs.Screen
          name="training-spotify"
          options={{
            title: 'Training Spotify',
            href: null, // Don't show in navigation
          }}
        />
        <Tabs.Screen
          name="social_new"
          options={{
            title: 'Social New',
            href: null, // Don't show in navigation
          }}
        />
        <Tabs.Screen
          name="home"
          options={{
            title: 'Home Alt',
            href: null, // Don't show in navigation
          }}
        />
      </Tabs>

      <FloatingWorkoutTracker />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
});
