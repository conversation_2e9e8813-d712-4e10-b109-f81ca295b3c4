{"name": "elite-locker", "main": "./index.js", "version": "1.0.0", "scripts": {"start": "expo start", "dev": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint", "postinstall": "patch-package"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-segmented-control/segmented-control": "^2.5.7", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.3.10", "@supabase/supabase-js": "^2.49.4", "@tradle/react-native-http": "^2.0.1", "assert": "^2.1.0", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "events": "^3.3.0", "expo": "53.0.9", "expo-background-fetch": "^13.1.5", "expo-blur": "~14.1.4", "expo-calendar": "^14.1.4", "expo-constants": "~17.1.6", "expo-crypto": "^14.1.4", "expo-file-system": "~18.1.9", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-location": "~18.1.5", "expo-router": "~5.0.7", "expo-sharing": "^13.1.5", "expo-speech-recognition": "^2.0.0", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-task-manager": "^13.1.5", "expo-web-browser": "~14.1.6", "https-browserify": "^1.0.0", "path-browserify": "^1.0.1", "querystring-es3": "^0.2.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-level-fs": "^3.0.1", "react-native-os": "^1.2.6", "react-native-polyfill-globals": "^3.1.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "^15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-view-shot": "^4.0.3", "react-native-web": "~0.20.0", "react-native-websocket": "^1.0.2", "react-native-webview": "13.13.5", "stream-browserify": "^3.0.0", "url": "^0.11.4", "util": "^0.12.5", "web-streams-polyfill": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "typescript": "~5.8.3"}, "private": true}