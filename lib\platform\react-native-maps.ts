/**
 * Platform-specific wrapper for react-native-maps
 * This conditionally imports the real module on native platforms
 * and provides a web fallback for web builds
 */

import { Platform } from 'react-native';

// Type definitions for the components we use
export interface MapViewProps {
  style?: any;
  provider?: string;
  region?: {
    latitude: number;
    longitude: number;
    latitudeDelta: number;
    longitudeDelta: number;
  };
  customMapStyle?: any[];
  children?: React.ReactNode;
}

export interface MarkerProps {
  coordinate: {
    latitude: number;
    longitude: number;
  };
  title?: string;
  children?: React.ReactNode;
}

export interface PolylineProps {
  coordinates: Array<{
    latitude: number;
    longitude: number;
  }>;
  strokeColor?: string;
  strokeWidth?: number;
}

// Platform-specific imports
let MapView: React.ComponentType<MapViewProps>;
let Marker: React.ComponentType<MarkerProps>;
let Polyline: React.ComponentType<PolylineProps>;
let PROVIDER_GOOGLE: string;
let PROVIDER_DEFAULT: string;

if (Platform.OS === 'web') {
  // Web fallback - import our mock components
  const webMaps = require('../mocks/react-native-maps.web.js');
  MapView = webMaps.default;
  Marker = webMaps.Marker;
  Polyline = webMaps.Polyline;
  PROVIDER_GOOGLE = webMaps.PROVIDER_GOOGLE;
  PROVIDER_DEFAULT = webMaps.PROVIDER_DEFAULT;
} else {
  // Native platforms - import the real react-native-maps
  const nativeMaps = require('react-native-maps');
  MapView = nativeMaps.default;
  Marker = nativeMaps.Marker;
  Polyline = nativeMaps.Polyline;
  PROVIDER_GOOGLE = nativeMaps.PROVIDER_GOOGLE;
  PROVIDER_DEFAULT = nativeMaps.PROVIDER_DEFAULT;
}

// Export all components and constants
export default MapView;
export {
  MapView,
  Marker,
  Polyline,
  PROVIDER_GOOGLE,
  PROVIDER_DEFAULT,
};
