import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React from 'react';
import { RunTrackingProvider } from '../contexts/RunTrackingContext';
import { WorkoutProvider } from '../contexts/WorkoutContext';
import { WorkoutPurchaseProvider } from '../contexts/WorkoutPurchaseContext';

export default function RootLayout() {
  return (
    <WorkoutPurchaseProvider>
      <RunTrackingProvider>
        <WorkoutProvider>
          <StatusBar style="light" />
          <Stack
            screenOptions={{
              headerShown: false,
              contentStyle: { backgroundColor: '#000000' },
            }}
          />
        </WorkoutProvider>
      </RunTrackingProvider>
    </WorkoutPurchaseProvider>
  );
}
